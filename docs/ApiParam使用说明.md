# FormAttrDTO.DataSource API参数扩展说明

## 概述

为了满足更灵活的API参数需求，我们对`FormAttrDTO.DataSource`中的`params`字段进行了扩展，现在支持多种参数类型，如`dependencyParams`、`apiParams`等，且这些参数对应的值为key-value形式的JSON字符串。

## 主要变更

### 1. 数据结构变更

**FormAttrDTO.DataSource.Api.params字段类型变更：**
- 原来：`String params`
- 现在：`Map<String, Object> params`

**FormAttr.DataSource.Api.params字段类型变更：**
- 原来：`String params`  
- 现在：`Map<String, Object> params`

### 2. 注解结构变更

**ComponentProperty.DataSource注解变更：**
- 原来：`String apiParams() default ""`
- 现在：`ApiParam[] apiParams() default {}`

**新增ApiParam注解：**
```java
@interface ApiParam {
    /**
     * 参数类型，如：params、dependencyParams、apiParams等
     */
    String key();
    
    /**
     * 参数值（JSON字符串格式）
     */
    String value();
}
```

## 使用示例

### 1. 注解使用示例

```java
@ComponentProperty(
    label = "所属分厂", 
    path = "plantCode", 
    searchable = true,
    initialRequest = true, 
    dataSourceType = DataSourceType.API,
    dataSource = @ComponentProperty.DataSource(
        apiUrl = "/craft/craft/findPlantList",
        httpMethod = HttpMethod.GET,
        apiParams = {
            @ComponentProperty.ApiParam(
                key = "params", 
                value = "{\"status\":\"active\",\"type\":\"plant\"}"
            ),
            @ComponentProperty.ApiParam(
                key = "dependencyParams", 
                value = "{\"userId\":\"${currentUserId}\"}"
            ),
            @ComponentProperty.ApiParam(
                key = "apiParams", 
                value = "{\"version\":\"v1\",\"format\":\"json\"}"
            )
        },
        mapping = @ComponentProperty.Mapping(
            valueKey = "plantCode",
            labelKey = "plantName"
        )
    )
)
private String plantCode;
```

### 2. 生成的数据结构

上述注解会生成如下的params结构：

```json
{
  "params": {
    "status": "active",
    "type": "plant"
  },
  "dependencyParams": {
    "userId": "${currentUserId}"
  },
  "apiParams": {
    "version": "v1",
    "format": "json"
  }
}
```

### 3. Excel导入支持

在Excel导入时，apiParams字段仍然支持JSON字符串格式：

```json
{
  "params": {"status": "active"},
  "dependencyParams": {"userId": "${currentUserId}"}
}
```

## 兼容性说明

1. **向后兼容**：现有的代码仍然可以正常工作
2. **导入功能**：Excel导入功能已更新，支持新的参数结构
3. **数据转换**：系统会自动处理JSON字符串的解析和转换

## 注意事项

1. **JSON格式**：ApiParam的value必须是有效的JSON字符串
2. **参数类型**：key字段直接代表参数类型，常用的有：
   - `params`：基础参数
   - `dependencyParams`：依赖参数
   - `apiParams`：API特定参数
3. **错误处理**：如果JSON解析失败，系统会将原始字符串作为参数值

## 迁移指南

如果您有现有的代码需要迁移到新的结构：

1. **注解迁移**：将原来的字符串参数改为ApiParam数组
2. **数据迁移**：现有的MongoDB数据会在读取时自动转换
3. **测试验证**：建议在迁移后进行充分的测试

## 示例场景

### 场景1：简单参数
```java
apiParams = {
    @ComponentProperty.ApiParam(key = "params", value = "{\"limit\":10}")
}
```

### 场景2：多种参数类型
```java
apiParams = {
    @ComponentProperty.ApiParam(key = "params", value = "{\"status\":\"active\"}"),
    @ComponentProperty.ApiParam(key = "dependencyParams", value = "{\"userId\":\"${currentUserId}\"}"),
    @ComponentProperty.ApiParam(key = "headers", value = "{\"Authorization\":\"Bearer ${token}\"}")
}
```

### 场景3：复杂参数结构
```java
apiParams = {
    @ComponentProperty.ApiParam(
        key = "queryParams", 
        value = "{\"filter\":{\"status\":\"active\",\"type\":\"plant\"},\"sort\":{\"name\":\"asc\"}}"
    )
}
```
