package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.SysDictItem;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductionTaskRepository extends BaseRepository<ProductionTask, String> {
    /**
     * 查询状态为statusCode的任务
     *
     * @param statusCode 任务状态
     * <AUTHOR>
     * @date 2025/3/24 14:22
     */
    List<ProductionTask> findByStatusCodeAndDeleted(@NotNull SysDictItem statusCode, Boolean deleted);

    /**
     * 根据任务编号查询任务
     * @param taskCode
     * @return
     */
    ProductionTask findFirstByTaskCodeAndDeleted(String taskCode,  Boolean deleted);


    @Query("UPDATE ProductionTask t SET t.statusCode = :status WHERE t.taskId = :taskId")
    @Modifying
    void changeStatus(
            @Param("taskId") String taskId,
            @Param("status") SysDictItem status
    );
}