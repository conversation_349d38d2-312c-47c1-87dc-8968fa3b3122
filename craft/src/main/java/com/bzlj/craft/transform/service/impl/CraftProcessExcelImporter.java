package com.bzlj.craft.transform.service.impl;

import com.bzlj.craft.entity.CraftProcess;
import com.bzlj.craft.entity.Plant;
import com.bzlj.craft.entity.ProcessStep;
import com.bzlj.craft.repository.CraftProcessRepository;
import com.bzlj.craft.repository.PlantRepository;
import com.bzlj.craft.repository.ProcessStepRepository;
import com.bzlj.craft.transform.constant.ExcelImportPolicyConstants;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service(value = ExcelImportPolicyConstants.EXCEL_IMPORT_POLICY + "process")
public class CraftProcessExcelImporter implements ExcelImportPolicy<CraftProcess> {

    private final PlantRepository plantRepository;

    private final CraftProcessRepository craftProcessRepository;

    private final DataPrepareService dataPrepareService;

    private final ProcessStepRepository processStepRepository;

    public CraftProcessExcelImporter(PlantRepository plantRepository,
                                     CraftProcessRepository craftProcessRepository,
                                     DataPrepareService dataPrepareService,
                                     ProcessStepRepository processStepRepository) {
        this.plantRepository = plantRepository;
        this.craftProcessRepository = craftProcessRepository;
        this.dataPrepareService = dataPrepareService;
        this.processStepRepository = processStepRepository;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<CraftProcess> importFromExcel(InputStream inputStream){
        ImportResult<CraftProcess> results = new ImportResult<>();

        List<CraftProcess> processes = parseExcel(inputStream);
        List<String> excelCodes = processes.stream()
                .map(CraftProcess::getProcessCode)
                .collect(Collectors.toList());

        // 批量查询已存在的编码
        List<String> existingCodes = craftProcessRepository.findExistingCodes(excelCodes);

        // 过滤重复数据
        List<CraftProcess> newProcesses = processes.stream()
                .filter(p -> !existingCodes.contains(p.getProcessCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newProcesses)) {
            results.setSize(0);
            results.setResults(List.of());
        }
        List<CraftProcess> craftProcesses = craftProcessRepository.saveAll(newProcesses);
        //创建同名称工步
        List<ProcessStep> steps = craftProcesses.stream().map(process -> {
            ProcessStep processStep = new ProcessStep();
            processStep.setProcess(process);
            processStep.setStepOrder(1);
            processStep.setParamConfig(Map.of());
            processStep.setQualityStandard(Map.of());
            processStep.setStepName(process.getProcessName());
            return processStep;
        }).toList();
        processStepRepository.saveAll(steps);
        results.setSize(craftProcesses.size());
        results.setResults(craftProcesses);
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CraftProcess> parseExcel(InputStream inputStream) {
        List<CraftProcess> processes = new ArrayList<>();
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 默认读取第一个Sheet

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头

                String processCode = getCellStringValue(row.getCell(0));
                String processName = getCellStringValue(row.getCell(1));
                String plantName = getCellStringValue(row.getCell(2));
                Plant plant = dataPrepareService.getPlant(plantName);
                if(Objects.isNull(plant)){
                    plant = new Plant();
                    plant.setPlantCode(plantName);
                    plant.setPlantName(plantName);
                    plant = plantRepository.save(plant);
                    dataPrepareService.setPlat(plant);
                }

                // 构建CraftProcess
                CraftProcess process = new CraftProcess();
                process.setProcessCode(processCode);
                process.setProcessName(processName);
                process.setPlant(plant);
                process.setProcessOrder(1); // 默认值，按需调整
                process.setInputOutputSpec(Map.of()); // 默认空JSON

                processes.add(process);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally{
            try {
                workbook.close();
            }catch (IOException e){
                throw new RuntimeException("Failed to close workbook", e);
            }
        }
        return processes;
    }
}