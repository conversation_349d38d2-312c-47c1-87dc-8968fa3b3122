package com.bzlj.craft.service.impl;

import com.bzlj.craft.dto.CraftProcessDTO;
import com.bzlj.craft.dto.PlantDTO;
import com.bzlj.craft.dto.ProcessStepDTO;
import com.bzlj.craft.dto.StepParameterDTO;
import com.bzlj.craft.entity.CraftProcess;
import com.bzlj.craft.entity.Plant;
import com.bzlj.craft.entity.ProcessStep;
import com.bzlj.craft.entity.StepParameter;
import com.bzlj.craft.repository.CraftProcessRepository;
import com.bzlj.craft.repository.PlantRepository;
import com.bzlj.craft.repository.ProcessStepRepository;
import com.bzlj.craft.repository.StepParameterRepository;
import com.bzlj.craft.service.ICraftService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 工序评分
 * @date 2025-03-10 13:38
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CraftServiceImpl implements ICraftService {

    @PersistenceContext
    private EntityManager entityManager;


    @Autowired
    private PlantRepository plantRepository;

    @Autowired
    private CraftProcessRepository craftProcessRepository;

    @Autowired
    private ProcessStepRepository processStepRepository;

    @Autowired
    private StepParameterRepository stepParameterRepository;


    @Override
    public List<PlantDTO> findPlantList() {
        List<Plant> plants = plantRepository.findAll(Sort.by(Sort.Order.desc("updatedAt")));
        if (!CollectionUtils.isEmpty(plants)) {
            return plants.stream().map(plant -> {
                PlantDTO plantDTO = new PlantDTO();
                BeanUtils.copyProperties(plant, plantDTO);
                return plantDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    public List<CraftProcessDTO> findProcessByPlantCode(String plantCode) {
        List<CraftProcess> craftProcesses = craftProcessRepository.findByPlantPlantCodeOrderByProcessOrderAsc(plantCode);
        if (!CollectionUtils.isEmpty(craftProcesses)) {
            return craftProcesses.stream().map(craftProcess -> {
                CraftProcessDTO craftProcessDTO = new CraftProcessDTO();
                BeanUtils.copyProperties(craftProcess, craftProcessDTO);
                return craftProcessDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    public List<ProcessStepDTO> findProcessStepByProcessId(String processId) {
        List<ProcessStep> processSteps = processStepRepository.findByProcessIdOrderByStepOrderAsc(processId);
        if (!CollectionUtils.isEmpty(processSteps)) {
            return processSteps.stream().map(processStep -> {
                ProcessStepDTO processStepDTO = new ProcessStepDTO();
                BeanUtils.copyProperties(processStep, processStepDTO);
                return processStepDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    public List<StepParameterDTO> findStepParameterByStepId(String stepId) {
        List<StepParameter> stepParameters = stepParameterRepository.findByStepIdOrderByCreatedTime(stepId);
        if (!CollectionUtils.isEmpty(stepParameters)) {
            return stepParameters.stream().map(stepParameter -> {
                StepParameterDTO stepParameterDTO = new StepParameterDTO();
                BeanUtils.copyProperties(stepParameter, stepParameterDTO);
                return stepParameterDTO;
            }).toList();
        }
        return List.of();
    }

}



