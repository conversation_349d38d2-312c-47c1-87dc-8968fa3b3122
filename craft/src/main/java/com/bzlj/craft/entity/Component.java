package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-14 10:49
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "component_copy")
public class Component implements Serializable {

    @Column(name = "component_id", nullable = false)
    private String componentId;

    @Id
    @Column(name = "component_key", nullable = false)
    private String componentKey;

    @Column(name = "group_code")
    private String groupCode;

    @Column(name = "model")
    private String model;

    @Column(name = "method_path")
    private String methodPath;

    @Column(name = "params")
    private String params;

    @Column(name = "is_loop")
    private Boolean isLoop;

    /**
     * 轮询间隔 毫秒
     */
    @Column(name = "loop_interval")
    private Long loopInterval;
}
